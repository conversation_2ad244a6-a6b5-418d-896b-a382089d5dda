import { ILogger, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Op, fn } from 'sequelize';
import { Complaint, Customer, Order, Employee } from '../entity';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import { ComplaintStatus } from '../common/Constant';

@Provide()
export class ComplaintService extends BaseService<Complaint> {
  @Inject()
  ctx: Context;

  @Inject()
  logger: ILogger;

  constructor() {
    super('投诉建议');
  }

  getModel() {
    return Complaint;
  }

  /**
   * 创建投诉建议（用户端）
   * @param customerId 客户ID
   * @param complaintData 投诉建议数据
   */
  async createComplaint(
    customerId: number,
    complaintData: {
      category: 'complaint' | 'suggestion';
      subCategory: 'order' | 'employee' | 'platform' | 'service';
      title: string;
      content: string;
      orderId?: number;
      employeeId?: number;
      contactInfo?: string;
      photoURLs?: string[];
    }
  ) {
    const {
      category,
      subCategory,
      title,
      content,
      orderId,
      employeeId,
      contactInfo,
      photoURLs,
    } = complaintData;

    // 用户端创建投诉建议时，客户ID必须存在
    if (!customerId) {
      throw new CustomError('客户ID不能为空', 400);
    }

    // 验证客户是否存在
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new CustomError('客户不存在', 404);
    }

    // 如果是订单投诉，验证订单是否存在且属于该客户，并获取服务人员信息
    let orderEmployeeId = employeeId;
    if (subCategory === 'order' && orderId) {
      const order = await Order.findOne({
        where: { id: orderId, customerId },
        attributes: ['id', 'customerId', 'employeeId'],
      });
      if (!order) {
        throw new CustomError('订单不存在或不属于该客户', 404);
      }

      // 如果订单有服务人员信息，自动设置员工ID
      if (order.employeeId) {
        orderEmployeeId = order.employeeId;
        this.logger.info(
          `订单投诉自动关联服务人员: orderId=${orderId}, employeeId=${order.employeeId}`
        );
      }
    }

    // 如果是人员投诉，验证员工是否存在
    if (subCategory === 'employee' && employeeId) {
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new CustomError('员工不存在', 404);
      }
    }

    // 创建投诉建议
    const complaint = await Complaint.create({
      customerId,
      category,
      subCategory,
      title,
      content,
      orderId,
      employeeId: orderEmployeeId, // 使用从订单获取的员工ID或原始传入的员工ID
      contactInfo,
      photoURLs: photoURLs || [],
      status: ComplaintStatus.PENDING,
    });

    // 返回包含关联信息的完整数据
    return await this.getComplaintWithRelations(complaint.id);
  }

  /**
   * 获取包含关联信息的投诉建议
   * @param complaintId 投诉建议ID
   */
  async getComplaintWithRelations(complaintId: number) {
    return await Complaint.findByPk(complaintId, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
        },
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
        },
      ],
    });
  }

  /**
   * 更新投诉建议
   * @param customerId 客户ID
   * @param complaintId 投诉建议ID
   * @param updateData 更新数据
   */
  async updateComplaint(
    customerId: number,
    complaintId: number,
    updateData: {
      category?: 'complaint' | 'suggestion';
      subCategory?: 'order' | 'employee' | 'platform' | 'service';
      title?: string;
      content?: string;
      orderId?: number;
      employeeId?: number;
      contactInfo?: string;
      photoURLs?: string[];
    }
  ) {
    const complaint = await Complaint.findOne({
      where: { id: complaintId, customerId },
    });

    if (!complaint) {
      throw new CustomError('投诉建议不存在或不属于该客户', 404);
    }

    // 只有待处理状态的投诉建议才能修改
    if (complaint.status !== ComplaintStatus.PENDING) {
      throw new CustomError('只有待处理状态的投诉建议才能修改', 400);
    }

    // 验证关联数据并处理订单投诉的员工ID自动设置
    const finalUpdateData = { ...updateData };

    if (updateData.orderId && updateData.subCategory === 'order') {
      const order = await Order.findOne({
        where: { id: updateData.orderId, customerId },
        attributes: ['id', 'customerId', 'employeeId'],
      });
      if (!order) {
        throw new CustomError('订单不存在或不属于该客户', 404);
      }

      // 如果订单有服务人员信息，自动设置员工ID
      if (order.employeeId) {
        finalUpdateData.employeeId = order.employeeId;
        this.logger.info(
          `更新订单投诉自动关联服务人员: orderId=${updateData.orderId}, employeeId=${order.employeeId}`
        );
      }
    }

    if (updateData.employeeId && updateData.subCategory === 'employee') {
      const employee = await Employee.findByPk(updateData.employeeId);
      if (!employee) {
        throw new CustomError('员工不存在', 404);
      }
    }

    await complaint.update(finalUpdateData);

    return await this.getComplaintWithRelations(complaintId);
  }

  /**
   * 删除投诉建议
   * @param complaintId 投诉建议ID
   */
  async deleteComplaint(complaintId: number) {
    const complaint = await Complaint.findOne({
      where: { id: complaintId },
    });

    if (!complaint) {
      throw new CustomError('投诉建议不存在或不属于该客户', 404);
    }

    // 只有待处理状态的投诉建议才能删除
    if (complaint.status !== ComplaintStatus.PENDING) {
      throw new CustomError('只有待处理状态的投诉建议才能删除', 400);
    }

    await complaint.destroy();
    return true;
  }

  /**
   * 处理投诉建议
   * @param complaintId 投诉建议ID
   * @param handleData 处理数据
   */
  async handleComplaint(
    complaintId: number,
    handleData: {
      status: 'processing' | 'resolved' | 'closed';
      result: string;
      handlerId: number;
    }
  ) {
    const complaint = await Complaint.findByPk(complaintId);

    if (!complaint) {
      throw new CustomError('投诉建议不存在', 404);
    }

    // 验证处理人员是否存在（这里假设handlerId是管理员ID，实际可能需要验证管理员表）
    // const handler = await Admin.findByPk(handleData.handlerId);
    // if (!handler) {
    //   throw new CustomError('处理人员不存在', 404);
    // }

    await complaint.update({
      status: handleData.status,
      result: handleData.result,
      handlerId: handleData.handlerId,
      handledAt: new Date(),
    });

    return await this.getComplaintWithRelations(complaintId);
  }

  /**
   * 获取客户的投诉建议列表
   * @param customerId 客户ID
   * @param page 页码
   * @param pageSize 每页数量
   */
  async getCustomerComplaints(customerId: number, page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;

    const result = await this.findAll({
      query: { customerId },
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });

    return {
      ...result,
      page,
      pageSize,
      totalPages: Math.ceil(result.total / pageSize),
    };
  }

  /**
   * 获取投诉建议统计信息
   * @param filters 过滤条件
   */
  async getComplaintStatistics(
    filters: {
      startDate?: string;
      endDate?: string;
      category?: 'complaint' | 'suggestion';
      subCategory?: 'order' | 'employee' | 'platform' | 'service';
    } = {}
  ) {
    const whereCondition: any = {};

    // 处理日期范围
    if (filters.startDate || filters.endDate) {
      whereCondition.createdAt = {};
      if (filters.startDate) {
        whereCondition.createdAt[Op.gte] = new Date(filters.startDate);
      }
      if (filters.endDate) {
        whereCondition.createdAt[Op.lte] = new Date(
          filters.endDate + ' 23:59:59'
        );
      }
    }

    // 处理类型过滤
    if (filters.category) {
      whereCondition.category = filters.category;
    }
    if (filters.subCategory) {
      whereCondition.subCategory = filters.subCategory;
    }

    // 总数统计
    const total = await Complaint.count({ where: whereCondition });

    // 按状态统计
    const statusStats = await Complaint.findAll({
      where: whereCondition,
      attributes: ['status', [fn('COUNT', '*'), 'count']],
      group: ['status'],
      raw: true,
    });

    // 按类型统计
    const categoryStats = await Complaint.findAll({
      where: whereCondition,
      attributes: ['category', [fn('COUNT', '*'), 'count']],
      group: ['category'],
      raw: true,
    });

    // 按子类型统计
    const subCategoryStats = await Complaint.findAll({
      where: whereCondition,
      attributes: ['subCategory', [fn('COUNT', '*'), 'count']],
      group: ['subCategory'],
      raw: true,
    });

    return {
      total,
      statusStats,
      categoryStats,
      subCategoryStats,
    };
  }

  /**
   * 员工创建建议
   * @param employeeId 员工ID
   * @param suggestionData 建议数据
   */
  async createEmployeeSuggestion(
    employeeId: number,
    suggestionData: {
      subCategory: 'platform' | 'service';
      title: string;
      content: string;
      contactInfo?: string;
      photoURLs?: string[];
    }
  ) {
    const { subCategory, title, content, contactInfo, photoURLs } =
      suggestionData;

    // 验证员工是否存在
    const employee = await Employee.findByPk(employeeId);
    if (!employee) {
      throw new CustomError('员工不存在', 404);
    }

    // 创建员工建议，customerId设置为null
    const complaint = await Complaint.create({
      customerId: null, // 员工建议不关联客户
      employeeId,
      category: 'suggestion', // 员工只能创建建议
      subCategory,
      title,
      content,
      contactInfo,
      photoURLs: photoURLs || [],
      status: ComplaintStatus.PENDING,
    });

    // 返回包含关联信息的完整数据
    return await this.getEmployeeSuggestionWithRelations(complaint.id);
  }

  /**
   * 获取包含关联信息的员工建议
   * @param suggestionId 建议ID
   */
  async getEmployeeSuggestionWithRelations(suggestionId: number) {
    return await Complaint.findByPk(suggestionId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
        },
      ],
    });
  }

  /**
   * 员工更新建议
   * @param employeeId 员工ID
   * @param suggestionId 建议ID
   * @param updateData 更新数据
   */
  async updateEmployeeSuggestion(
    employeeId: number,
    suggestionId: number,
    updateData: {
      subCategory?: 'platform' | 'service';
      title?: string;
      content?: string;
      contactInfo?: string;
      photoURLs?: string[];
    }
  ) {
    const complaint = await Complaint.findOne({
      where: {
        id: suggestionId,
        employeeId,
        category: 'suggestion', // 确保只能操作建议类型
      },
    });

    if (!complaint) {
      throw new CustomError('建议不存在或不属于该员工', 404);
    }

    // 只有待处理状态的建议才能修改
    if (complaint.status !== ComplaintStatus.PENDING) {
      throw new CustomError('只有待处理状态的建议才能修改', 400);
    }

    await complaint.update(updateData);

    return await this.getEmployeeSuggestionWithRelations(suggestionId);
  }

  /**
   * 员工删除建议
   * @param employeeId 员工ID
   * @param suggestionId 建议ID
   */
  async deleteEmployeeSuggestion(employeeId: number, suggestionId: number) {
    const complaint = await Complaint.findOne({
      where: {
        id: suggestionId,
        employeeId,
        category: 'suggestion', // 确保只能操作建议类型
      },
    });

    if (!complaint) {
      throw new CustomError('建议不存在或不属于该员工', 404);
    }

    // 只有待处理状态的建议才能删除
    if (complaint.status !== ComplaintStatus.PENDING) {
      throw new CustomError('只有待处理状态的建议才能删除', 400);
    }

    await complaint.destroy();
    return true;
  }

  /**
   * 获取员工的建议列表
   * @param employeeId 员工ID
   * @param filters 过滤条件
   * @param page 页码
   * @param pageSize 每页数量
   */
  async getEmployeeSuggestions(
    employeeId: number,
    filters: {
      subCategory?: 'platform' | 'service';
      status?: 'pending' | 'processing' | 'resolved' | 'closed';
      keyword?: string;
      startDate?: string;
      endDate?: string;
    } = {},
    page = 1,
    pageSize = 10
  ) {
    const offset = (page - 1) * pageSize;

    // 构建查询条件
    const whereCondition: any = {
      employeeId,
      category: 'suggestion', // 只查询建议类型
    };

    // 处理过滤条件
    if (filters.subCategory) {
      whereCondition.subCategory = filters.subCategory;
    }
    if (filters.status) {
      whereCondition.status = filters.status;
    }

    // 处理日期范围查询
    if (filters.startDate || filters.endDate) {
      whereCondition.createdAt = {};
      if (filters.startDate) {
        whereCondition.createdAt[Op.gte] = new Date(filters.startDate);
      }
      if (filters.endDate) {
        whereCondition.createdAt[Op.lte] = new Date(
          filters.endDate + ' 23:59:59'
        );
      }
    }

    // 处理关键词搜索（标题和内容）
    if (filters.keyword) {
      whereCondition[Op.or] = [
        { title: { [Op.like]: `%${filters.keyword}%` } },
        { content: { [Op.like]: `%${filters.keyword}%` } },
      ];
    }

    const result = await this.findAll({
      query: whereCondition,
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
        },
      ],
    });

    return {
      ...result,
      page,
      pageSize,
      totalPages: Math.ceil(result.total / pageSize),
    };
  }

  /**
   * 管理员录入投诉建议
   * @param adminId 管理员ID
   * @param complaintData 投诉建议数据
   */
  async adminCreateComplaint(
    adminId: number,
    complaintData: {
      category: 'complaint' | 'suggestion';
      subCategory: 'order' | 'employee' | 'platform' | 'service' | 'workflow';
      title: string;
      content: string;
      customerId?: number;
      orderId?: number;
      employeeId?: number;
      contactInfo?: string;
      photoURLs?: string[];
      adminNote?: string;
    }
  ) {
    const {
      category,
      subCategory,
      title,
      content,
      customerId,
      orderId,
      employeeId,
      contactInfo,
      photoURLs,
      adminNote,
    } = complaintData;

    // 验证客户是否存在（如果提供了客户ID）
    if (customerId) {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        throw new CustomError('客户不存在', 404);
      }
    }

    // 验证订单是否存在且属于该客户（如果提供了订单ID）
    let orderEmployeeId = employeeId;
    if (subCategory === 'order' && orderId) {
      const whereCondition: any = { id: orderId };
      if (customerId) {
        whereCondition.customerId = customerId;
      }

      const order = await Order.findOne({
        where: whereCondition,
        attributes: ['id', 'customerId', 'employeeId'],
      });
      if (!order) {
        throw new CustomError(
          customerId ? '订单不存在或不属于该客户' : '订单不存在',
          404
        );
      }

      // 如果订单有服务人员信息，自动设置员工ID
      if (order.employeeId) {
        orderEmployeeId = order.employeeId;
        this.logger.info(
          `管理员录入订单投诉自动关联服务人员: orderId=${orderId}, employeeId=${order.employeeId}`
        );
      }
    }

    // 验证员工是否存在（如果提供了员工ID）
    if (subCategory === 'employee' && employeeId) {
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new CustomError('员工不存在', 404);
      }
    }

    // 创建投诉建议
    const complaint = await Complaint.create({
      customerId: customerId || null,
      category,
      subCategory,
      title,
      content,
      orderId,
      employeeId: orderEmployeeId,
      contactInfo,
      photoURLs: photoURLs || [],
      status: ComplaintStatus.PENDING,
      createdBy: adminId,
      adminNote,
    });

    // 返回包含关联信息的完整数据
    return await this.getComplaintWithRelations(complaint.id);
  }

  /**
   * 管理员更新投诉建议
   * @param complaintId 投诉建议ID
   * @param updateData 更新数据
   */
  async adminUpdateComplaint(
    complaintId: number,
    updateData: {
      category?: 'complaint' | 'suggestion';
      subCategory?: 'order' | 'employee' | 'platform' | 'service' | 'workflow';
      title?: string;
      content?: string;
      customerId?: number;
      orderId?: number;
      employeeId?: number;
      contactInfo?: string;
      photoURLs?: string[];
      adminNote?: string;
    }
  ) {
    const complaint = await Complaint.findByPk(complaintId);

    if (!complaint) {
      throw new CustomError('投诉建议不存在', 404);
    }

    // 验证关联数据
    const finalUpdateData = { ...updateData };

    // 验证客户是否存在（如果提供了客户ID）
    if (updateData.customerId) {
      const customer = await Customer.findByPk(updateData.customerId);
      if (!customer) {
        throw new CustomError('客户不存在', 404);
      }
    }

    // 验证订单是否存在且属于该客户（如果提供了订单ID）
    if (updateData.subCategory === 'order' && updateData.orderId) {
      const whereCondition: any = { id: updateData.orderId };
      if (updateData.customerId) {
        whereCondition.customerId = updateData.customerId;
      }

      const order = await Order.findOne({
        where: whereCondition,
        attributes: ['id', 'customerId', 'employeeId'],
      });
      if (!order) {
        throw new CustomError(
          updateData.customerId ? '订单不存在或不属于该客户' : '订单不存在',
          404
        );
      }

      // 如果订单有服务人员信息，自动设置员工ID
      if (order.employeeId) {
        finalUpdateData.employeeId = order.employeeId;
        this.logger.info(
          `管理员更新订单投诉自动关联服务人员: orderId=${updateData.orderId}, employeeId=${order.employeeId}`
        );
      }
    }

    // 验证员工是否存在（如果提供了员工ID）
    if (updateData.subCategory === 'employee' && updateData.employeeId) {
      const employee = await Employee.findByPk(updateData.employeeId);
      if (!employee) {
        throw new CustomError('员工不存在', 404);
      }
    }

    await complaint.update(finalUpdateData);

    return await this.getComplaintWithRelations(complaintId);
  }

  /**
   * 管理员删除投诉建议（无状态限制）
   * @param complaintId 投诉建议ID
   */
  async adminDeleteComplaint(complaintId: number) {
    const complaint = await Complaint.findByPk(complaintId);

    if (!complaint) {
      throw new CustomError('投诉建议不存在', 404);
    }

    // 管理员可以删除任何状态的投诉建议
    await complaint.destroy();
    return true;
  }
}
